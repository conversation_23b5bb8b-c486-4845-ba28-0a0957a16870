// MinIO 
const minioBucketName = process.env.MINIO_BUCKETNAME;
const minioEndpoint = process.env.MINIO_ENDPOINT;
const minioAccessKey = process.env.MINIO_ACCESS_KEY;
const minioSecretKey = process.env.MINIO_SECRET_KEY;


const minioEndpointPort = 9020;
const minioSSL = false;

const Minio = require('minio');

const minioClient = new Minio.Client({
    endPoint: minioEndpoint, // замените на адрес вашего MinIO
    port: minioEndpointPort, // порт по умолчанию для MinIO
    useSSL: minioSSL, // установите true, если используете HTTPS
    accessKey: minioAccessKey, // замените на свой аксесс ключ
    secretKey: minioSecretKey // замените на свой секретный ключ
});



async function uploadMinio(inputFile) {
    if (!minioBucketName) {
        throw new Error(`You must set the "BUCKET" environment variable.`);
    } else {
        const uploadResult = await minioClient.fPutObject(minioBucketName, inputFile, path.resolve(inputFile), {
            'Content-Type': 'audio/mp4'
        });

        console.log(uploadResult);

        if (uploadResult) {
            console.log('File uploaded successfully to MinIO');
        } else {
            console.error('Error uploading file to MinIO');
        }
    }
}


async function minioGetLink(req, res) {
    const fileName = req.query.name;
    try {
        const stat = await minioClient.statObject(minioBucketName, fileName);
        if (stat) {
            const url = await minioClient.presignedGetObject(minioBucketName, fileName);
            res.json({ url });
        } else {
            res.status(404).send('File found but can not be getted');
        }
    } catch (err) {
        res.status(500).send('Error getting file');
    }
}


async function uploadToMinio(req, res) {
    const file = req.file;
    console.log('File:', file);

    if (!file) {
        console.log('No file uploaded');
        return res.status(400).send('No file uploaded');
    }

    const fileName = file.originalname;
    console.log('File name:', fileName);

    try {
        console.log('Uploading file to MinIO...');
        await minioClient.fPutObject(minioBucketName, fileName, file.path);
        console.log('File uploaded to MinIO');

        fs.unlinkSync(file.path); // удаляем временный файл

        const url = await minioClient.presignedGetObject(minioBucketName, fileName);
        console.log('Generated pre-signed URL:', url);

        res.json({
            name: fileName,
            url
        });
    } catch (err) {
        console.error('Error uploading file:', err);
        fs.unlinkSync(file.path); // удаляем временный файл в случае ошибки
        res.status(500).send('Error uploading file');
    }
}

module.exports = {
    minioGetLink,
    uploadToMinio
};

