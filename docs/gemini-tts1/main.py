# main.py

import argparse
import os
import logging

# Импортируем оба модуля синтеза
import tts_module_gemini
import tts_module_edge

# Настройка логирования
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)


def save_file(file_name: str, data: str | bytes):
    """Безопасно сохраняет данные в файл (бинарно или текстово)."""
    try:
        mode = "wb" if isinstance(data, bytes) else "w"
        encoding = None if isinstance(data, bytes) else "utf-8"
        with open(file_name, mode, encoding=encoding) as f:
            f.write(data)
        logging.info(f"Файл успешно сохранен: {file_name}")
    except IOError as e:
        logging.error(f"Не удалось сохранить файл {file_name}: {e}")


def process_text_file(args):
    """
    Основной процесс: читает файл, вызывает выбранный движок и сохраняет результат(ы).
    """
    input_path = args.input_file
    if not os.path.exists(input_path):
        logging.error(f"Файл не найден по пути: {input_path}")
        return

    logging.info(f"Открытие файла для чтения: {input_path}")
    try:
        with open(input_path, "r", encoding="utf-8") as f:
            text_content = f.read()
        if not text_content.strip():
            logging.warning("Входной файл пуст. Обработка прекращена.")
            return
    except Exception as e:
        logging.error(f"Ошибка при чтении файла {input_path}: {e}")
        return

    base_name = os.path.splitext(input_path)[0]

    # --- Выбор движка ---
    if args.engine == "edge":
        logging.info(f"Выбран движок Edge-TTS с голосом '{args.voice}'")
        logging.info(
            "Идет синтез аудио и субтитров... Это может занять некоторое время."
        )

        audio_data, srt_data = tts_module_edge.synthesize_text_to_audio_and_srt(
            text_content, voice=args.voice
        )

        if audio_data:
            # Edge-TTS по умолчанию кодирует в MP3
            output_audio_path = f"{base_name}.mp3"
            save_file(output_audio_path, audio_data)
        if srt_data:
            output_srt_path = f"{base_name}.srt"
            if args.save_srt:
                save_file(output_srt_path, srt_data)
            else:
                logging.info("Сохранение SRT-субтитров пропущено по запросу.")

        if not audio_data and not srt_data:
            logging.error(
                "Не удалось сгенерировать аудио и субтитры с помощью Edge-TTS."
            )

    elif args.engine == "gemini":
        logging.info("Выбран движок Gemini")
        logging.info(
            "Обращение к Gemini API для синтеза аудио... Это может занять некоторое время."
        )

        audio_data = tts_module_gemini.synthesize_text_to_audio(text_content)

        if audio_data:
            # Gemini модуль настроен на вывод WAV
            output_audio_path = f"{base_name}.wav"
            save_file(output_audio_path, audio_data)
        else:
            logging.error(
                "Не удалось сгенерировать аудио с помощью Gemini. Проверьте API ключ и сообщения об ошибках."
            )


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Преобразование текста из файла в речь с помощью выбранного движка."
    )
    parser.add_argument(
        "input_file",
        type=str,
        help="Путь к входному файлу (.txt или .md), содержащему текст для озвучивания.",
    )
    parser.add_argument(
        "--engine",
        type=str,
        choices=["edge", "gemini"],
        default="edge",
        help="Движок для синтеза речи: 'edge' (по умолчанию, локально) или 'gemini' (требует API ключ).",
    )
    parser.add_argument(
        "--voice",
        type=str,
        default="ru-RU-SvetlanaNeural",
        help="Голос для движка Edge-TTS (например, 'en-US-JennyNeural'). Используйте 'edge-tts --list-voices', чтобы увидеть все варианты.",
    )
    parser.add_argument(
        "--save-srt",
        action="store_true",
        help="Сохранять ли SRT-субтитры вместе с аудиофайлом. По умолчанию False.",
    )

    args = parser.parse_args()
    process_text_file(args)
