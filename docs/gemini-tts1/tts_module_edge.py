# tts_module_edge.py

import logging
import edge_tts
from io import BytesIO
import re
from markdown_cleaner import (
    convert_to_plain_text,
)  # Импортируем функцию для очистки Markdown

# Укажем голос по умолчанию. 'ru-RU-SvetlanaNeural' - хороший выбор для русского языка.
DEFAULT_VOICE = "ru-RU-SvetlanaNeural"
# ru-RU-DmitryNeural
# ru-RU-SvetlanaNeural


def _clean_text(text: str) -> str:
    """Очищает текст от Markdown, эмодзи и некоторых специальных символов,
    сохраняя знаки препинания.
    """
    # Сначала очищаем Markdown с помощью специализированной библиотеки
    cleaned_text = convert_to_plain_text(text)

    # Паттерн для удаления эмодзи (может быть доработан для более полного охвата)
    emoji_pattern = re.compile(
        "["
        "\U0001f600-\U0001f64f"
        "\U0001f300-\U0001f5ff"
        "\U0001f680-\U0001f6ff"
        "\U0001f1e0-\U0001f1ff"
        "\U00002702-\U000027b0"
        "\U000024c2-\U0001f251"
        "]+",
        flags=re.UNICODE,
    )

    # Удаляем эмодзи
    cleaned_text = emoji_pattern.sub(r"", cleaned_text)

    # Удаляем символы, которые не должны быть озвучены, но сохраняем пунктуацию.
    # Этот паттерн удаляет: *, #, _, @, $, %, ^, &, ~, =, +, <, >, |
    # Он не затрагивает: . , ! ? ; : " ' ` ( ) [ ] { } -
    # Если требуется удалить другие символы, или оставить какие-то из удаляемых, нужно изменить этот паттерн.
    # cleaned_text = re.sub(r"[\*#_@$%^&~=+<>|]+", " ", cleaned_text)
    cleaned_text = re.sub(r"[@$%^&~=<>|]+", " ", cleaned_text)

    # Заменяем множественные пробелы на одиночные и удаляем пробелы в начале/конце строки
    cleaned_text = re.sub(r"\s+", " ", cleaned_text).strip()

    return cleaned_text


def _synthesize_sync(text: str, voice: str) -> tuple[bytes | None, str | None]:
    """Синхронная функция для выполнения синтеза."""
    # Очистка текста перед синтезом
    cleaned_text = _clean_text(text)

    logging.info(
        f"Начало синхронного синтеза для текста: '{cleaned_text[:50]}...' с голосом: {voice}"
    )
    audio_stream = BytesIO()
    sub_maker = edge_tts.SubMaker()

    try:
        logging.info("Инициализация Edge-TTS Communicate.")
        communicate = edge_tts.Communicate(
            cleaned_text,  # Используем очищенный текст
            voice,
            rate="+40%",  # Установка скорости по умолчанию +70%
        )
        notified = False
        accumulated_audio_bytes = 0
        chunk_threshold = 100 * 1024  # 100 KB
        for chunk in communicate.stream_sync():
            if chunk["type"] == "audio":
                if not notified:
                    logging.info("Первый чанк аудио получен.")
                    notified = True
                audio_stream.write(chunk["data"])
                accumulated_audio_bytes += len(chunk["data"])
                if accumulated_audio_bytes >= chunk_threshold:
                    logging.info(
                        f"Получено и добавлено еще 100 КБ аудио данных. Всего: {audio_stream.tell() / 1024:.2f} КБ."
                    )
                    accumulated_audio_bytes = 0  # Сбрасываем счетчик
            elif chunk["type"] == "WordBoundary":
                sub_maker.feed(chunk)
        logging.info("Синтез аудиопотока завершен.")

        audio_bytes = audio_stream.getvalue()
        srt_content = sub_maker.get_srt()
        logging.info("Получены аудиобайты и SRT-субтитры.")
        return audio_bytes, srt_content

    except Exception as e:
        logging.error(f"Ошибка при синтезе речи через Edge-TTS: {e}")
        return None, None


def synthesize_text_to_audio_and_srt(
    text_to_speak: str, voice: str = DEFAULT_VOICE
) -> tuple[bytes | None, str | None]:
    """
    Синхронная обертка для синтеза речи и субтитров с помощью Edge-TTS.

    Args:
        text_to_speak: Текст для озвучивания.
        voice: Голос для синтеза (например, 'en-GB-SoniaNeural').

    Returns:
        Кортеж (audio_bytes, srt_string) или (None, None) в случае ошибки.
    """
    logging.info(
        f"Начало синхронной обертки для синтеза текста: '{text_to_speak[:50]}...'"
    )
    try:
        audio_data, srt_data = _synthesize_sync(text_to_speak, voice)
        logging.info("Синхронная операция завершена успешно.")
        return audio_data, srt_data
    except Exception as e:
        logging.error(f"Ошибка при синхронном синтезе Edge-TTS: {e}")
        return None, None
