# tts_module.py

import os
import struct
from dotenv import load_dotenv
from google import genai
from google.genai import types

# Загружаем переменные окружения из .env файла
load_dotenv()


def synthesize_text_to_audio(text_to_speak: str) -> bytes | None:
    """
    Синтезирует речь из текста с помощью Gemini API.

    Args:
        text_to_speak: Текст, который нужно озвучить.

    Returns:
        Байты аудио в формате WAV или None в случае ошибки.
    """
    try:
        api_key = os.environ.get("GEMINI_API_KEY")
        if not api_key:
            print("Ошибка: API-ключ GEMINI_API_KEY не найден. Проверьте ваш .env файл.")
            return None

        client = genai.Client(api_key=api_key)
        model = "gemini-2.5-flash-preview-tts"

        contents = [
            types.Content(
                role="user",
                parts=[types.Part.from_text(text=text_to_speak)],
            ),
        ]

        generate_content_config = types.GenerateContentConfig(
            # temperature=1, # Not applicable for TTS
            response_modalities=["audio"],
            speech_config=types.SpeechConfig(
                voice_config=types.VoiceConfig(
                    prebuilt_voice_config=types.PrebuiltVoiceConfig(voice_name="Kore")
                )
            ),
        )

        # Gemini TTS API возвращает аудио в виде одного большого чанка,
        # но для совместимости будем обрабатывать его как поток
        full_audio_data = b""
        audio_mime_type = ""

        response_chunks = client.models.generate_content_stream(
            model=model,
            contents=contents,
            config=generate_content_config,
        )

        for chunk in response_chunks:
            if (
                chunk.candidates
                and chunk.candidates[0].content
                and chunk.candidates[0].content.parts
            ):
                part = chunk.candidates[0].content.parts[0]
                if part.inline_data and part.inline_data.data:
                    # Собираем все аудиоданные
                    full_audio_data += part.inline_data.data
                    if not audio_mime_type:
                        audio_mime_type = part.inline_data.mime_type
                elif chunk.text:
                    # В случае ошибки API может вернуть текст
                    print(f"API вернуло текстовое сообщение: {chunk.text}")

        if full_audio_data and audio_mime_type:
            # Преобразуем сырые данные в формат WAV
            return convert_to_wav(full_audio_data, audio_mime_type)

    except Exception as e:
        print(f"Произошла ошибка при обращении к Gemini API: {e}")

    return None


def convert_to_wav(audio_data: bytes, mime_type: str) -> bytes:
    """Генерирует заголовок WAV для аудиоданных и возвращает полный WAV-файл."""
    parameters = parse_audio_mime_type(mime_type)
    bits_per_sample = parameters["bits_per_sample"]
    sample_rate = parameters["rate"]
    num_channels = 1
    data_size = len(audio_data)
    bytes_per_sample = bits_per_sample // 8
    block_align = num_channels * bytes_per_sample
    byte_rate = sample_rate * block_align
    chunk_size = 36 + data_size

    header = struct.pack(
        "<4sI4s4sIHHIIHH4sI",
        b"RIFF",
        chunk_size,
        b"WAVE",
        b"fmt ",
        16,
        1,
        num_channels,
        sample_rate,
        byte_rate,
        block_align,
        bits_per_sample,
        b"data",
        data_size,
    )
    return header + audio_data


def parse_audio_mime_type(mime_type: str) -> dict[str, int]:
    """Извлекает битрейт и частоту дискретизации из MIME-типа."""
    bits_per_sample = 16
    rate = 24000

    parts = mime_type.split(";")
    for param in parts:
        param = param.strip()
        if param.lower().startswith("rate="):
            try:
                rate = int(param.split("=")[1])
            except (ValueError, IndexError):
                pass
        elif param.startswith("audio/L"):
            try:
                bits_per_sample = int(param.split("L")[1])
            except (ValueError, IndexError):
                pass

    return {"bits_per_sample": bits_per_sample, "rate": rate}
