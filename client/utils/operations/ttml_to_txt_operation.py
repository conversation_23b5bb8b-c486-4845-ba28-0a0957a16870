from client.utils.operations.base_operation import BaseOperation
from client.utils.converters.ttml_to_txt_converter import TtmlToTxtConverter
from loguru import logger
from questionary import Style


class TtmlToTxtOperation(BaseOperation):
    def __init__(self, custom_style: Style):
        super().__init__(custom_style)

    def run(self) -> None:
        logger.info("### Конвертация TTML в TXT ###")
        directory = self.get_directory_input(
            "Введите директорию с TTML файлами (текущая директория: .):"
        )

        self.confirm_execution("Начать конвертацию TTML в TXT?")

        converter = TtmlToTxtConverter(directory)
        converter.convert_all()
        logger.info("Конвертация TTML в TXT завершена.")
