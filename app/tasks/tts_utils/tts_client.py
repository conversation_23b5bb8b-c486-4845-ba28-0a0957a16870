import struct
from io import BytesIO
import re
import asyncio
import tempfile
from pathlib import Path
from google import genai
from google.genai import types
import edge_tts
from app.core.logging_config import logger
from app.core.config import settings
from app.tasks.tts_utils.markdown_cleaner import convert_to_plain_text


class TTSClient:
    def __init__(self):
        pass

    def _clean_text(self, text: str) -> str:
        """Очищает текст от Markdown, эмодзи и некоторых специальных символов,
        сохраняя знаки препинания.
        """
        cleaned_text = convert_to_plain_text(text)

        emoji_pattern = re.compile(
            "["
            "\U0001f600-\U0001f64f"
            "\U0001f300-\U0001f5ff"
            "\U0001f680-\U0001f6ff"
            "\U0001f1e0-\U0001f1ff"
            "\U00002702-\U000027b0"
            "\U000024c2-\U0001f251"
            "]+",
            flags=re.UNICODE,
        )

        cleaned_text = emoji_pattern.sub(r"", cleaned_text)
        cleaned_text = re.sub(r"[@$%^&~=<>|]+", " ", cleaned_text)
        cleaned_text = re.sub(r"\s+", " ", cleaned_text).strip()
        return cleaned_text

    def _parse_audio_mime_type(self, mime_type: str) -> dict[str, int]:
        """Извлекает битрейт и частоту дискретизации из MIME-типа."""
        bits_per_sample = 16
        rate = 24000

        parts = mime_type.split(";")
        for param in parts:
            param = param.strip()
            if param.lower().startswith("rate="):
                try:
                    rate = int(param.split("=")[1])
                except (ValueError, IndexError):
                    pass
            elif param.startswith("audio/L"):
                try:
                    bits_per_sample = int(param.split("L")[1])
                except (ValueError, IndexError):
                    pass
        return {"bits_per_sample": bits_per_sample, "rate": rate}

    def _convert_to_wav(self, audio_data: bytes, mime_type: str) -> bytes:
        """Генерирует заголовок WAV для аудиоданных и возвращает полный WAV-файл."""
        parameters = self._parse_audio_mime_type(mime_type)
        bits_per_sample = parameters["bits_per_sample"]
        sample_rate = parameters["rate"]
        num_channels = 1
        data_size = len(audio_data)
        bytes_per_sample = bits_per_sample // 8
        block_align = num_channels * bytes_per_sample
        byte_rate = sample_rate * block_align
        chunk_size = 36 + data_size

        header = struct.pack(
            "<4sI4s4sIHHIIHH4sI",
            b"RIFF",
            chunk_size,
            b"WAVE",
            b"fmt ",
            16,
            1,
            num_channels,
            sample_rate,
            byte_rate,
            block_align,
            bits_per_sample,
            b"data",
            data_size,
        )
        return header + audio_data

    async def _convert_wav_to_mp3(self, wav_data: bytes) -> bytes | None:
        """Конвертирует WAV данные в MP3 используя ffmpeg-python."""
        try:
            import ffmpeg
            from concurrent.futures import ThreadPoolExecutor

            # Создаем временный файл для WAV данных
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as wav_file:
                wav_file.write(wav_data)
                wav_path = Path(wav_file.name)

            mp3_path = wav_path.with_suffix(".mp3")

            # Создаем ffmpeg процесс с помощью ffmpeg-python
            # Используем ThreadPoolExecutor для запуска синхронного кода в отдельном потоке
            async def run_ffmpeg_conversion():
                try:
                    # Создаем ffmpeg процесс
                    stream = ffmpeg.input(str(wav_path))
                    stream = ffmpeg.output(
                        stream,
                        str(mp3_path),
                        codec="libmp3lame",
                        audio_bitrate="128k",
                        y=None,  # -y для перезаписи файла
                    )

                    # Запускаем процесс в отдельном потоке, чтобы не блокировать event loop
                    with ThreadPoolExecutor() as executor:
                        await asyncio.get_event_loop().run_in_executor(
                            executor,
                            lambda: ffmpeg.run(
                                stream,
                                capture_stdout=True,
                                capture_stderr=True,
                                quiet=True,
                            ),
                        )

                    # Читаем MP3 данные
                    mp3_data = mp3_path.read_bytes()
                    return mp3_data
                except ffmpeg.Error as e:
                    logger.error(
                        f"Ошибка ffmpeg: {e.stderr.decode() if e.stderr else str(e)}"
                    )
                    return None
                finally:
                    # Удаляем временные файлы
                    wav_path.unlink(missing_ok=True)
                    mp3_path.unlink(missing_ok=True)

            # Запускаем конвертацию
            mp3_data = await run_ffmpeg_conversion()

            if mp3_data:
                logger.info("Успешно конвертировано WAV в MP3")
                return mp3_data
            return None

        except Exception as e:
            logger.error(f"Ошибка при конвертации WAV в MP3: {e}", exc_info=True)
            # Убедимся, что временные файлы удалены даже при ошибке
            try:
                if "wav_path" in locals():
                    wav_path.unlink(missing_ok=True)
                if "mp3_path" in locals():
                    mp3_path.unlink(missing_ok=True)
            except Exception as cleanup_error:
                logger.error(f"Ошибка при удалении временных файлов: {cleanup_error}")
            return None

    async def synthesize_gemini(self, text_to_speak: str, voice: str) -> bytes | None:
        try:
            # Очищаем текст так же, как для Edge TTS
            cleaned_text = self._clean_text(text_to_speak)
            logger.info(
                f"Начало синтеза Gemini для текста: '{cleaned_text[:50]}...' с голосом: {voice}"
            )

            api_key = settings.GEMINI_API_KEY
            if not api_key:
                logger.error("Ошибка: API-ключ GEMINI_API_KEY не найден.")
                return None

            client = genai.Client(api_key=api_key)
            model = "gemini-2.5-flash-preview-tts"

            contents = [
                types.Content(
                    role="user",
                    parts=[types.Part.from_text(text=cleaned_text)],
                ),
            ]

            generate_content_config = types.GenerateContentConfig(
                response_modalities=["audio"],
                speech_config=types.SpeechConfig(
                    voice_config=types.VoiceConfig(
                        prebuilt_voice_config=types.PrebuiltVoiceConfig(
                            voice_name=voice
                        )
                    )
                ),
            )

            full_audio_data = b""
            audio_mime_type = ""

            response_chunks = client.models.generate_content_stream(
                model=model,
                contents=contents,
                config=generate_content_config,
            )

            for chunk in response_chunks:
                if (
                    chunk.candidates
                    and chunk.candidates[0].content
                    and chunk.candidates[0].content.parts
                ):
                    part = chunk.candidates[0].content.parts[0]
                    if part.inline_data and part.inline_data.data:
                        full_audio_data += part.inline_data.data
                        if not audio_mime_type:
                            audio_mime_type = part.inline_data.mime_type
                    elif chunk.text:
                        logger.warning(
                            f"Gemini API вернуло текстовое сообщение: {chunk.text}"
                        )

            if full_audio_data and audio_mime_type:
                # Сначала конвертируем в WAV
                wav_data = self._convert_to_wav(full_audio_data, audio_mime_type)
                if wav_data:
                    # Затем конвертируем WAV в MP3
                    mp3_data = await self._convert_wav_to_mp3(wav_data)
                    if mp3_data:
                        logger.info("Gemini TTS синтез завершен успешно")
                        return mp3_data
                    else:
                        logger.error("Не удалось конвертировать WAV в MP3")
                else:
                    logger.error("Не удалось создать WAV из данных Gemini")

        except Exception as e:
            logger.error(
                f"Произошла ошибка при обращении к Gemini API: {e}", exc_info=True
            )

        return None

    async def synthesize_edge(
        self, text_to_speak: str, voice: str, rate: str
    ) -> bytes | None:
        cleaned_text = self._clean_text(text_to_speak)

        logger.info(
            f"Начало синтеза для текста: '{cleaned_text[:50]}...' с голосом: {voice}"
        )
        audio_stream = BytesIO()
        # sub_maker = edge_tts.SubMaker() # Not needed for just audio synthesis

        try:
            logger.info("Инициализация Edge-TTS Communicate.")
            communicate = edge_tts.Communicate(
                cleaned_text,
                voice,
                rate=rate,
            )

            async for chunk in communicate.stream():
                if chunk["type"] == "audio":
                    audio_stream.write(chunk["data"])
            logger.info("Синтез аудиопотока завершен.")

            audio_bytes = audio_stream.getvalue()
            logger.info("Получены аудиобайты.")
            return audio_bytes

        except Exception as e:
            logger.error(f"Ошибка при синтезе речи через Edge-TTS: {e}", exc_info=True)
            return None

    async def synthesize(self, text: str, engine: str) -> bytes | None:
        if engine.lower() == "gemini":
            return await self.synthesize_gemini(text)
        elif engine.lower() == "edge":
            return await self.synthesize_edge(text)
        else:
            logger.error(f"Неподдерживаемый движок TTS: {engine}")
            return None
