import asyncio
from pathlib import Path
import tempfile
import urllib.parse

from app.features.tts.service import TTSService
from app.features.tts.models import TTSEngine
from app.tasks.base_orchestrator import BaseTaskOrchestrator
from app.tasks.tts_utils.tts_client import TTSClient
from app.core.exceptions import WorkerFatalError, WorkerTransientError
from app.core.config import settings

# Define a directory for storing audio files. Ensure this directory exists and is accessible.
# Changed to use a temporary directory as per user's request.
AUDIO_FILE_DIR = Path(tempfile.mkdtemp(prefix="tts_audio_"))


class TTSOrchestrator(BaseTaskOrchestrator):
    def __init__(self, ctx: dict, text: str, engine: str, task_id: str):
        super().__init__(task_id=task_id, db_session_factory=ctx["db_session_factory"])
        self.ctx = ctx
        self.text = text
        try:
            self.text_hash, engine_str = task_id.rsplit("_tts_", 1)
            self.engine = TTSEngine(urllib.parse.unquote(engine_str))
        except (ValueError, KeyError) as e:
            raise WorkerFatalError(f"Could not parse task_id: {task_id}") from e
        self.tts_client = TTSClient()

    async def _execute_task(self) -> dict:
        self.log.info("Начало выполнения задачи TTS")

        file_path = None
        file_size_bytes = 0
        try:
            if self.engine == TTSEngine.EDGE:
                audio_bytes = await self.tts_client.synthesize_edge(
                    text_to_speak=self.text,
                    voice=settings.EDGE_TTS_VOICE,
                    rate=settings.EDGE_TTS_RATE,
                )
                file_extension = "mp3"
            elif self.engine == TTSEngine.GEMINI:
                audio_bytes = await self.tts_client.synthesize_gemini(
                    text_to_speak=self.text, voice=settings.GEMINI_TTS_VOICE
                )
                file_extension = "wav"
            else:
                raise WorkerFatalError(f"Неподдерживаемый движок TTS: {self.engine}")

            # Сохранение аудиофайла
            filename = f"{self.text_hash}.{file_extension}"
            file_path = AUDIO_FILE_DIR / filename
            file_path.parent.mkdir(parents=True, exist_ok=True)
            await asyncio.to_thread(file_path.write_bytes, audio_bytes)
            file_size_bytes = len(audio_bytes)
            self.log.info(f"Аудиофайл сохранен: {file_path}")

            # Update TTS status in DB using original text_hash and engine
            async with self.db_session_factory() as session:
                tts_service = TTSService(session, None)
                await tts_service.update_tts_status(
                    text_hash=self.text_hash,
                    engine=self.engine,
                    file_path=str(file_path),
                    file_size_bytes=file_size_bytes,
                    text=self.text,
                    file_name=filename,
                )

        except Exception as e:
            self.log.error(f"Ошибка синтеза TTS: {e}", exc_info=True)
            raise WorkerTransientError(f"Ошибка синтеза TTS: {e}") from e

        return {
            "text_hash": self.text_hash,
            "engine": self.engine.value,
            "file_path": str(file_path),  # Store as string for DB
            "file_size_bytes": file_size_bytes,
        }

    async def _get_final_result(self, result_data: dict) -> dict:
        return {
            "text_hash": result_data["text_hash"],
            "engine": result_data["engine"],
            "file_size_bytes": result_data["file_size_bytes"],
            "get_url": f"/tts/{self.task_id}",
        }
