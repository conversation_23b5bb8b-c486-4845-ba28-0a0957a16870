import asyncio
import tempfile
from loguru import logger
from typing import Callable

from app.features.youtube_audio import service as youtube_audio_service
from app.features.youtube_audio.schemas import YouTubeAudioResult
from app.core.exceptions import YouTubeAudioDownloadError, WorkerFatalError
from app.tasks.youtube_audio_utils.extractors import YtDlpAudioExtractor
from app.tasks.base_orchestrator import BaseTaskOrchestrator

AUDIO_DOWNLOAD_ATTEMPTS = 3
AUDIO_DOWNLOAD_TIMEOUT = 90  # seconds


class YouTubeAudioOrchestrator(BaseTaskOrchestrator):
    def __init__(
        self,
        video_url: str,
        task_id: str,
        db_session_factory: Callable,
    ):
        super().__init__(task_id, db_session_factory)
        self.video_url = video_url
        self.download_dir = tempfile.mkdtemp(prefix="ytaudio_")
        self.extractor = YtDlpAudioExtractor(progress_hook=self._download_progress_hook)
        logger.debug(
            f"[AudioOrchestrator] Инициализировано: video_url={self.video_url}, task_id={self.task_id}, download_dir={self.download_dir}"
        )

    def _download_progress_hook(self, d):
        if d.get("status") == "downloading":
            logger.debug(
                f"[AudioOrchestrator] Загрузка {self.video_url}: {d.get('_percent_str')} из {d.get('_total_bytes_str')} со скоростью {d.get('_speed_str')}"
            )
        elif d.get("status") == "finished":
            logger.info(f"[AudioOrchestrator] Загрузка завершена: {d.get('filename')}")

    async def _execute_task(self) -> dict:
        last_exc = None
        for attempt in range(1, AUDIO_DOWNLOAD_ATTEMPTS + 1):
            try:
                logger.info(
                    f"[AudioOrchestrator] Попытка {attempt}/{AUDIO_DOWNLOAD_ATTEMPTS} загрузки аудио для {self.video_url}"
                )
                audio_data = await asyncio.wait_for(
                    self.extractor.extract(self.video_url, self.download_dir),
                    timeout=AUDIO_DOWNLOAD_TIMEOUT,
                )
                async with self.db_session_factory() as db_session:
                    youtube_audio_entry = await youtube_audio_service.save_audio_to_db(
                        db_session, audio_data
                    )
                    result_data = YouTubeAudioResult(
                        video_id=youtube_audio_entry.video_id,
                        file_path=youtube_audio_entry.file_path,
                        file_name=youtube_audio_entry.file_name,
                        file_size_bytes=youtube_audio_entry.file_size_bytes,
                        created_at=youtube_audio_entry.created_at,
                        audio_stream_url=f"/ytaudio/{youtube_audio_entry.video_id}/stream",
                    ).model_dump(mode="json")
                    return result_data
            except asyncio.TimeoutError:
                logger.warning(
                    f"[AudioOrchestrator] Таймаут (>{AUDIO_DOWNLOAD_TIMEOUT}с) на попытке {attempt} для {self.video_url}"
                )
                last_exc = YouTubeAudioDownloadError(
                    f"Таймаут после {AUDIO_DOWNLOAD_TIMEOUT}с"
                )
            except YouTubeAudioDownloadError as e:
                logger.warning(
                    f"[AudioOrchestrator] Ошибка загрузки на попытке {attempt}: {e}"
                )
                last_exc = e
            except Exception as e:
                logger.error(
                    f"[AudioOrchestrator] Непредвиденная ошибка на попытке {attempt}: {e}"
                )
                last_exc = e
            if attempt == AUDIO_DOWNLOAD_ATTEMPTS:
                if isinstance(last_exc, WorkerFatalError):
                    raise last_exc
                elif isinstance(last_exc, YouTubeAudioDownloadError):
                    raise last_exc
                else:
                    raise WorkerFatalError(
                        f"Непредвиденная ошибка во время обработки аудио: {last_exc}"
                    ) from last_exc
