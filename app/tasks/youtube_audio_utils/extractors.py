import asyncio
import os
from typing import Dict, Any
import yt_dlp
from loguru import logger

from app.core.exceptions import YouTubeAudioDownloadError, WorkerFatalError
from app.tasks.youtube_audio_utils.ydl_config import build_audio_ydl_options


class BaseAudioExtractor:
    """
    Базовый класс для стратегий скачивания аудио с YouTube.
    """

    async def extract(self, video_url: str, download_dir: str) -> Dict[str, Any]:
        raise NotImplementedError


class YtDlpAudioExtractor(BaseAudioExtractor):
    """
    Основная стратегия скачивания аудио через yt-dlp.
    """

    def __init__(self, progress_hook=None):
        self.progress_hook = progress_hook

    async def extract(self, video_url: str, download_dir: str) -> Dict[str, Any]:
        loop = asyncio.get_running_loop()
        return await loop.run_in_executor(
            None, self._download_audio_sync, video_url, download_dir
        )

    def _download_audio_sync(self, video_url: str, download_dir: str) -> Dict[str, Any]:
        logger.debug(
            f"[AudioExtractor] Начало загрузки yt-dlp: url={video_url}, dir={download_dir}"
        )
        ydl_opts = build_audio_ydl_options(
            download_dir=download_dir, progress_hook=self.progress_hook
        )
        try:
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(video_url, download=True)
                file_path = os.path.join(download_dir, f"{info['id']}.{info['ext']}")
                logger.info(
                    f"[AudioExtractor] Успех: url={video_url}, файл={file_path}"
                )
                return {
                    "video_id": info["id"],
                    "file_path": file_path,
                    "file_name": info["title"],
                    "file_size_bytes": os.path.getsize(file_path),
                }
        except yt_dlp.utils.DownloadError as e:
            if "proxy" in str(e).lower() or "resolve host" in str(e).lower():
                raise WorkerFatalError(f"Неисправимая ошибка сети/прокси: {e}") from e
            raise YouTubeAudioDownloadError(f"Не удалось загрузить аудио: {e}") from e
        except Exception as e:
            logger.error(f"[AudioExtractor] Непредвиденная ошибка: {e}")
            raise
