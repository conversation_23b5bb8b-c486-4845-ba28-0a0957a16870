from app.tasks.youtube_audio_utils.orchestrator import YouTubeAudioOrchestrator
from app.core.exceptions import WorkerFatalError


async def download_youtube_audio(ctx: dict, video_url: str, task_id: str) -> None:
    """
    ARQ-задача для загрузки YouTube аудио.
    Создает и запускает экземпляр YouTubeAudioOrchestrator.
    """
    try:
        orchestrator = YouTubeAudioOrchestrator(
            video_url=video_url,
            task_id=task_id,
            db_session_factory=ctx["db_session_factory"],
        )
        await orchestrator.run()
    except WorkerFatalError as e:
        # Фатальные ошибки нужно пробрасывать выше, чтобы ARQ мог
        # корректно обработать ситуацию (например, остановить воркер).
        # Логирование уже произошло внутри оркестратора.
        raise e
