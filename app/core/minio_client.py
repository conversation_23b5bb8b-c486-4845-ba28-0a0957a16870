"""MinIO S3 client for file storage operations."""

import async<PERSON>
from pathlib import Path
from typing import Optional
from datetime import timed<PERSON><PERSON>
from minio import Minio
from minio.error import S3Error
from app.core.config import settings
from app.core.logging_config import logger
import tempfile


class MinIOClient:
    """MinIO S3 client for file storage operations."""

    def __init__(self):
        """Initialize MinIO client with configuration from settings."""
        self.client = Minio(
            endpoint=f"{settings.MINIO_ENDPOINT}:{settings.MINIO_PORT}",
            access_key=settings.MINIO_ACCESS_KEY,
            secret_key=settings.MINIO_SECRET_KEY,
            secure=settings.MINIO_USE_SSL,
        )
        self.bucket_name = settings.MINIO_BUCKET_NAME
        self._s3_available = None  # Cache S3 availability status
        self._check_s3_availability()

    def _check_s3_availability(self) -> None:
        """Check if S3 is available and cache the result."""
        try:
            # Try to list buckets to check connectivity
            list(self.client.list_buckets())
            self._ensure_bucket_exists()
            self._s3_available = True
            logger.info("S3 is available and configured")
        except Exception as e:
            self._s3_available = False
            logger.warning(f"S3 is not available: {e}. Will use local file storage.")

    def _ensure_bucket_exists(self) -> None:
        """Ensure the bucket exists, create if it doesn't."""
        try:
            if not self.client.bucket_exists(self.bucket_name):
                self.client.make_bucket(self.bucket_name)
                logger.info(f"Created MinIO bucket: {self.bucket_name}")
            else:
                logger.debug(f"MinIO bucket exists: {self.bucket_name}")
        except S3Error as e:
            logger.error(f"Error checking/creating bucket: {e}")
            raise

    def is_s3_available(self) -> bool:
        """Check if S3 is available."""
        return self._s3_available is True

    async def upload_file_safe(
        self,
        file_data: bytes,
        object_name: str,
        content_type: str = "application/octet-stream"
    ) -> Optional[str]:
        """
        Safely upload file data to MinIO with availability check.

        Args:
            file_data: Binary data to upload
            object_name: Name of the object in S3
            content_type: MIME type of the file

        Returns:
            S3 URL of the uploaded file if successful, None if S3 unavailable
        """
        if not self.is_s3_available():
            logger.debug("S3 not available, skipping upload")
            return None

        try:
            return await self.upload_file(file_data, object_name, content_type)
        except Exception as e:
            logger.error(f"Failed to upload to S3: {e}")
            # Mark S3 as unavailable for this session
            self._s3_available = False
            return None

    async def upload_file(
        self,
        file_data: bytes,
        object_name: str,
        content_type: str = "application/octet-stream",
    ) -> str:
        """
        Upload file data to MinIO.

        Args:
            file_data: Binary data to upload
            object_name: Name of the object in S3
            content_type: MIME type of the file

        Returns:
            S3 URL of the uploaded file
        """
        try:
            # Create a temporary file to upload
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                temp_file.write(file_data)
                temp_file_path = temp_file.name

            # Upload to MinIO in a thread to avoid blocking
            await asyncio.to_thread(
                self.client.fput_object,
                self.bucket_name,
                object_name,
                temp_file_path,
                content_type=content_type,
            )

            # Clean up temporary file
            Path(temp_file_path).unlink(missing_ok=True)

            # Generate S3 URL
            s3_url = f"s3://{self.bucket_name}/{object_name}"
            logger.info(f"File uploaded to MinIO: {s3_url}")
            return s3_url

        except S3Error as e:
            logger.error(f"Error uploading file to MinIO: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error uploading file: {e}")
            raise

    async def upload_file_from_path(
        self,
        file_path: Path,
        object_name: str,
        content_type: str = "application/octet-stream",
    ) -> str:
        """
        Upload file from local path to MinIO.

        Args:
            file_path: Path to the local file
            object_name: Name of the object in S3
            content_type: MIME type of the file

        Returns:
            S3 URL of the uploaded file
        """
        try:
            # Upload to MinIO in a thread to avoid blocking
            await asyncio.to_thread(
                self.client.fput_object,
                self.bucket_name,
                object_name,
                str(file_path),
                content_type=content_type,
            )

            # Generate S3 URL
            s3_url = f"s3://{self.bucket_name}/{object_name}"
            logger.info(f"File uploaded to MinIO: {s3_url}")
            return s3_url

        except S3Error as e:
            logger.error(f"Error uploading file to MinIO: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error uploading file: {e}")
            raise

    async def get_presigned_url(
        self, object_name: str, expires_seconds: int = 3600
    ) -> str:
        """
        Generate a presigned URL for downloading a file.

        Args:
            object_name: Name of the object in S3
            expires_seconds: URL expiration time in seconds (default: 1 hour)

        Returns:
            Presigned URL for downloading the file
        """
        try:
            url = await asyncio.to_thread(
                self.client.presigned_get_object,
                self.bucket_name,
                object_name,
                expires=timedelta(seconds=expires_seconds),
            )
            logger.debug(f"Generated presigned URL for {object_name}")
            return url
        except S3Error as e:
            logger.error(f"Error generating presigned URL: {e}")
            raise

    async def file_exists(self, object_name: str) -> bool:
        """
        Check if a file exists in MinIO.

        Args:
            object_name: Name of the object in S3

        Returns:
            True if file exists, False otherwise
        """
        try:
            await asyncio.to_thread(
                self.client.stat_object, self.bucket_name, object_name
            )
            return True
        except S3Error:
            return False
        except Exception as e:
            logger.error(f"Error checking file existence: {e}")
            return False

    async def delete_file(self, object_name: str) -> bool:
        """
        Delete a file from MinIO.

        Args:
            object_name: Name of the object in S3

        Returns:
            True if deleted successfully, False otherwise
        """
        try:
            await asyncio.to_thread(
                self.client.remove_object, self.bucket_name, object_name
            )
            logger.info(f"File deleted from MinIO: {object_name}")
            return True
        except S3Error as e:
            logger.error(f"Error deleting file from MinIO: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error deleting file: {e}")
            return False

    def extract_object_name_from_s3_url(self, s3_url: str) -> str:
        """
        Extract object name from S3 URL.

        Args:
            s3_url: S3 URL in format s3://bucket/object_name

        Returns:
            Object name
        """
        if s3_url.startswith(f"s3://{self.bucket_name}/"):
            return s3_url[len(f"s3://{self.bucket_name}/") :]
        raise ValueError(f"Invalid S3 URL format: {s3_url}")


# Global MinIO client instance
minio_client = MinIOClient()
