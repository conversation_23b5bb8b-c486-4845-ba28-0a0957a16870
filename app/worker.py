import google.genai as genai
from loguru import logger
from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker
from arq.jobs import Job
import asyncio
import aiohttp
from asyncio import timeout

from app.core.config import settings
from app.core.db import redis_settings
from app.tasks.subtitles import download_subtitles, WorkerFatalError
from app.tasks.summarization import summarize_text
from app.tasks.youtube_audio import download_youtube_audio
from app.tasks.youtube_audio_converter import convert_and_stream_audio
from app.tasks.tts import synthesize_audio


async def check_network_and_ip():
    """
    Проверяет доступность интернета, получает публичный IP-адрес и его приблизительное местоположение.
    """
    logger.info("Выполнение проверки сети...")
    try:
        async with timeout(10):
            async with aiohttp.ClientSession() as session:
                async with session.get("https://ipinfo.io/json") as response:
                    response.raise_for_status()  # Вызовет исключение для статусов 4xx/5xx
                    data = await response.json()

                    ip = data.get("ip", "Неизвестно")
                    city = data.get("city", "Неизвестно")
                    region = data.get("region", "Неизвестно")
                    country = data.get("country", "Неизвестно")
                    loc = data.get("loc", "Неизвестно")  # Координаты (широта, долгота)

                    logger.debug(f"[{ip}] Публичный IP-адрес воркера: {ip}")
                    logger.debug(
                        f"[{ip}] Местоположение: {city}, {region}, {country} (Координаты: {loc})"
                    )

    except aiohttp.ClientError as e:
        logger.warning(f"Ошибка HTTP-запроса при проверке сети: {e}")
        logger.warning("Не удалось получить публичный IP-адрес и местоположение.")
    except asyncio.TimeoutError:
        logger.warning("Таймаут при проверке сети. Возможно, нет доступа к интернету.")
    except Exception as e:
        logger.error(f"Непредвиденная ошибка при проверке сети: {e}")


async def on_startup(ctx):
    """
    Инициализация и проверка доступности внешних ресурсов при старте воркера.
    """
    logger.info("Запуск воркера...")

    # Проверка сети и получение IP
    await check_network_and_ip()

    # 1. Проверка и инициализация клиента Gemini
    if not settings.GEMINI_API_KEY:
        logger.critical(
            "Переменная окружения GEMINI_API_KEY не установлена. Воркер не может быть запущен."
        )
        raise ValueError("GEMINI_API_KEY environment variable not set.")

    ctx["genai_client"] = genai.Client(api_key=settings.GEMINI_API_KEY)
    logger.info("Клиент Gemini настроен.")

    # 2. Создание и проверка соединения с базой данных
    logger.info(f"Подключение к базе данных по адресу {settings.DATABASE_URL}...")
    try:
        engine = create_async_engine(
            settings.DATABASE_URL,
            echo=False,
            pool_size=10,  # количество постоянных соединений
            max_overflow=20,  # сколько дополнительных соединений можно создать при пике
            pool_recycle=1800,  # время жизни соединения (секунды)
        )
        async with engine.connect() as conn:
            await conn.execute(text("SELECT 1"))

        ctx["engine"] = engine
        ctx["db_session_factory"] = async_sessionmaker(engine, expire_on_commit=False)
        logger.success(
            "Подключение к базе данных успешно установлено. Фабрика сессий для воркера инициализирована."
        )
    except Exception as e:
        logger.critical(
            f"Ошибка подключения к базе данных: {e}. Воркер не будет запущен."
        )
        raise


async def on_shutdown(ctx):
    """
    Корректное освобождение ресурсов при завершении работы воркера.
    """
    logger.info("Завершение работы воркера...")
    engine = ctx.get("engine")
    if engine:
        await engine.dispose()
        logger.info("Пул подключений к базе данных закрыт.")


async def on_job_start(ctx, job: Job = None) -> None:
    """
    Вызывается перед началом выполнения каждой задачи.
    """
    if job is not None:
        logger.debug(f"[{job.job_id}] Запуск задачи '{job.function}'...")
    else:
        logger.debug("Запуск задачи (детали недоступны)")
    # NOTE: Задержка в 0.3 секунды для избежания проблем доступа к внешним API.
    await asyncio.sleep(0.3)


async def on_unhandled_exception(
    ctx, job: Job, exc: Exception
) -> None:  # <-- ИЗМЕНЕНО: Context -> dict
    """
    Обработка необработанных исключений в задачах.
    """
    worker = ctx.get("worker")

    if isinstance(exc, WorkerFatalError):
        logger.critical(
            f"[{job.job_id}] Обнаружена критическая ошибка в задаче '{job.function}': {exc}. "
            "Воркер будет корректно завершен."
        )
        if worker:
            await worker.abort()
    else:
        logger.error(
            f"[{job.job_id}] Необработанное исключение в задаче '{job.function}': {exc}",
            exc_info=True,
        )


class WorkerSettings:
    """
    Основной конфигурационный класс для воркера ARQ.
    """

    functions = [
        download_subtitles,
        summarize_text,
        download_youtube_audio,
        convert_and_stream_audio,
        synthesize_audio,
    ]
    redis_settings = redis_settings

    # --- Жизненный цикл воркера ---
    on_startup = on_startup
    on_shutdown = on_shutdown

    # --- Жизненный цикл задачи ---
    on_job_start = on_job_start
    # on_job_end = on_job_end # Можно добавить, если нужно логирование по окончании

    # --- Обработка ошибок ---
    on_unhandled_exception = on_unhandled_exception

    # --- Производительность ---
    max_jobs = 50  # Определяет количество одновременно (параллельно) выполняемых задач в одном экземпляре ARQ-воркера.
    # job_timeout = 300 # Таймаут на выполнение одной задачи в секундах (полезно)
    # max_tries = 3 # Количество повторных попыток для упавших задач
