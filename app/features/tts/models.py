from datetime import datetime
from enum import Enum
from typing import Optional
from sqlmodel import Field, SQLModel, Column, text


class TTSEngine(str, Enum):
    EDGE = "edge"
    GEMINI = "gemini"


class TTSBase(SQLModel):
    text: str
    engine: TTSEngine = Field(default=TTSEngine.EDGE)
    file_path: Optional[str] = Field(nullable=True)  # Local file path (deprecated)
    file_name: Optional[str] = Field(nullable=True)
    file_size_bytes: Optional[int] = Field(nullable=True)
    s3_url: Optional[str] = Field(default=None, nullable=True)  # S3 URL for the file


class TTS(TTSBase, table=True):
    __tablename__ = "tts"
    text_hash: str = Field(primary_key=True, index=True, nullable=False)
    engine: TTSEngine = Field(primary_key=True, default=TTSEngine.EDGE)
    created_at: datetime = Field(
        sa_column=Column(default=text("CURRENT_TIMESTAMP"), nullable=False)
    )
