from datetime import datetime
from enum import Enum
from typing import Optional
from sqlmodel import Field, SQLModel, Column, text


class TTSEngine(str, Enum):
    EDGE = "edge"
    GEMINI = "gemini"


class TTSBase(SQLModel):
    text: str
    engine: TTSEngine = Field(default=TTSEngine.EDGE)
    file_path: Optional[str] = Field(nullable=True)
    file_name: Optional[str] = Field(nullable=True)
    file_size_bytes: Optional[int] = Field(nullable=True)


class TTS(TTSBase, table=True):
    __tablename__ = "tts"
    text_hash: str = Field(primary_key=True, index=True, nullable=False)
    created_at: datetime = Field(
        sa_column=Column(default=text("CURRENT_TIMESTAMP"), nullable=False)
    )
