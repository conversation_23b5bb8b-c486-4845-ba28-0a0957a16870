from pathlib import Path
from typing import Dict, Any, Optional
import urllib.parse
from sqlmodel.ext.asyncio.session import AsyncSession
from fastapi import Depends, Request, Response, Body, HTTPException, status
from arq.connections import ArqRedis
from loguru import logger
from fastapi.responses import StreamingResponse, RedirectResponse

from app.features.base_api import BaseAPIEndpoint
from app.features.tasks.models import TaskType
from app.core.db import get_db_session, get_redis_pool
from app.core.minio_client import minio_client
from . import schemas, service


class TTSAPIEndpoint(BaseAPIEndpoint):
    ROUTER_PREFIX = "/tts"
    ROUTER_TAGS = ["Text-to-Speech"]
    TASK_TYPE = TaskType.TTS  # Assuming you will add TTS to TaskType enum
    ARQ_TASK_NAME = "synthesize_audio"

    def _urlsafe_engine(self, engine: str) -> str:
        """Encode engine to be safe for use in URLs and as part of task_id."""
        return urllib.parse.quote(engine, safe="")

    def _get_task_id(self, request_data: schemas.TTSSubmitRequest) -> str:
        # Generate a unique hash for the text content
        text_hash = service.TTSService(None, None).generate_text_xxhash(
            request_data.text
        )
        # Combine text_hash and engine into a single task ID
        safe_engine = self._urlsafe_engine(request_data.engine)
        return f"{text_hash}_tts_{safe_engine}"

    async def _check_cache(
        self,
        request: Request,
        db_session: AsyncSession,
        task_id: str,  # Renamed to reflect composite nature
        request_data: schemas.TTSSubmitRequest,
    ) -> Optional[Dict[str, Any]]:
        # Extract the original text_hash and engine from the composite ID
        try:
            original_text_hash, engine_str = task_id.rsplit("_tts_", 1)
            engine = urllib.parse.unquote(engine_str)
        except ValueError:
            logger.warning(f"Could not parse task_id: {task_id}")
            return None

        cached_result = await service.TTSService(db_session, None).get_tts_status(
            original_text_hash, engine
        )
        if cached_result and cached_result.file_path:
            # Return a dictionary that matches the expected result format for BaseAPIEndpoint
            return {
                "get_url": f"/tts/{task_id}",
                "status": "completed",
                "file_size_bytes": cached_result.file_size_bytes,
                "text_hash": cached_result.text_hash,
                "engine": cached_result.engine,
            }
        return None

    def _get_arq_task_params(
        self,
        task_id: str,  # Renamed to reflect composite nature
        request_data: schemas.TTSSubmitRequest,
    ) -> Dict[str, Any]:
        # Pass the original text and engine separately to the ARQ task
        # The text_hash (original) will be derived by the worker from the job_id (composite)
        return {
            "text": request_data.text,
            "engine": request_data.engine,  # Pass the string value directly
            "task_id": task_id,
        }

    def _register_routes(self):
        @self.router.post(
            "/",
            response_model=schemas.TTSSubmitResponse,
            status_code=status.HTTP_202_ACCEPTED,
            summary="Synthesize audio from text",
            description="Submits a text for audio synthesis using the specified engine (edge or gemini). Returns a task ID for status tracking.",
        )
        async def submit_tts(
            request: Request,
            response: Response,
            req_body: schemas.TTSSubmitRequest = Body(
                ...,
                example={
                    "text": "Hello, world! This is a test.",
                    "engine": "edge",
                },
            ),
            db_session: AsyncSession = Depends(get_db_session),
            redis: ArqRedis = Depends(get_redis_pool),
        ):
            return await self._handle_request(
                request, response, req_body, db_session, redis
            )

        @self.router.get("/{task_id}", summary="Serve audio file by task_id")
        async def serve_audio_file_by_task_id(
            task_id: str, db_session: AsyncSession = Depends(get_db_session)
        ):
            try:
                text_hash, engine_str = task_id.rsplit("_tts_", 1)
                engine = urllib.parse.unquote(engine_str)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid task_id format",
                )

            tts_service = service.TTSService(db_session, None)
            tts_entry = await tts_service.get_tts_status(text_hash, engine)

            if not tts_entry:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="Audio file not found"
                )

            # Prefer S3 URL if available, fallback to local file
            if tts_entry.s3_url:
                try:
                    # Extract object name from S3 URL
                    object_name = minio_client.extract_object_name_from_s3_url(
                        tts_entry.s3_url
                    )
                    # Generate presigned URL for direct download
                    presigned_url = await minio_client.get_presigned_url(
                        object_name, expires_seconds=3600
                    )
                    # Redirect to presigned URL
                    return RedirectResponse(url=presigned_url, status_code=302)
                except Exception as e:
                    logger.error(f"Error generating presigned URL: {e}")
                    # Fallback to local file if S3 fails

            # Fallback to local file serving (for backward compatibility)
            if not tts_entry.file_path:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="Audio file not found"
                )

            file_path = Path(tts_entry.file_path)
            if not file_path.exists():
                logger.warning(f"Requested audio file not found at path: {file_path}")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Audio file not found on disk",
                )

            media_type = (
                "audio/mpeg" if tts_entry.file_name.endswith(".mp3") else "audio/wav"
            )

            def iterfile():
                with open(file_path, mode="rb") as file_like:
                    yield from file_like

            return StreamingResponse(
                content=iterfile(),
                media_type=media_type,
                headers={
                    "Content-Disposition": f'attachment; filename="{tts_entry.file_name}"'
                },
            )


# Create an instance and export the router
endpoint = TTSAPIEndpoint()
router = endpoint.router
