from typing import Optional
from sqlmodel import Session, select
from app.features.tts.models import TTS
from app.core.logging_config import logger
from arq.connections import ArqRedis
import xxhash


class TTSService:
    def __init__(self, db_session: Session, arq_redis: ArqRedis):
        self.db_session = db_session
        self.arq_redis = arq_redis

    def generate_text_xxhash(self, text: str) -> str:
        """Generates a XXH3 hash for the given text."""
        return xxhash.xxh3_64_hexdigest(text.strip().encode("utf-8"))

    async def submit_tts_task(self, text: str, engine: str) -> str:
        text_hash = self.generate_text_xxhash(text)
        logger.info(
            "Submitting TTS task",
            text_hash=text_hash,
            text_length=len(text),
            engine=engine,
        )

        # Check if TTS entry already exists for this text_hash and engine
        existing_tts_entry = (
            await self.db_session.execute(
                select(TTS).where(TTS.text_hash == text_hash, TTS.engine == engine)
            )
        ).first()
        if existing_tts_entry:
            logger.info(
                "TTS entry already exists for this text hash and engine. Returning existing text_hash.",
                text_hash=text_hash,
                engine=engine,
            )
            # Return the composite ID for consistency, though the API will rebuild it
            return f"{text_hash}_tts_{engine}"

        # Create a new TTS entry in the database
        new_tts_entry = TTS(
            text=text,
            engine=engine,
            text_hash=text_hash,
        )
        self.db_session.add(new_tts_entry)
        await self.db_session.commit()
        await self.db_session.refresh(new_tts_entry)

        # Enqueue the ARQ task
        await self.arq_redis.enqueue_job(
            "synthesize_audio",  # This will be the ARQ worker function name
            text=text,
            engine=engine,  # Pass engine directly as a string
            _job_id=f"{text_hash}_tts_{engine}",  # Explicitly set the composite job_id
        )
        logger.info(
            "TTS task enqueued successfully", text_hash=text_hash, engine=engine
        )
        return f"{text_hash}_tts_{engine}"

    async def get_tts_status(self, text_hash: str, engine: str) -> Optional[TTS]:
        logger.info("Fetching TTS status", text_hash=text_hash, engine=engine)
        tts_entry = (
            await self.db_session.execute(
                select(TTS).where(TTS.text_hash == text_hash, TTS.engine == engine)
            )
        ).first()
        if tts_entry:
            logger.info(
                "TTS entry found",
                text_hash=text_hash,
                engine=engine,
                status=tts_entry.file_path is not None,
            )
        else:
            logger.warning("TTS entry not found", text_hash=text_hash, engine=engine)
        return tts_entry

    async def update_tts_status(
        self,
        text_hash: str,
        engine: str,
        file_path: str,
        file_size_bytes: int,
        text: str,
        file_name: str,
    ) -> None:
        logger.info(
            "Updating TTS status",
            text_hash=text_hash,
            engine=engine,
            file_path=file_path,
            file_size_bytes=file_size_bytes,
        )
        tts_entry = (
            await self.db_session.execute(
                select(TTS).where(TTS.text_hash == text_hash, TTS.engine == engine)
            )
        ).first()
        if tts_entry:
            tts_entry.file_path = file_path
            tts_entry.file_name = f"{text_hash}.mp3"
            tts_entry.file_size_bytes = file_size_bytes
            self.db_session.add(tts_entry)
            logger.info(
                "TTS entry updated successfully", text_hash=text_hash, engine=engine
            )
        else:
            logger.info("TTS entry not found, creating a new one.")
            tts_entry = TTS(
                text_hash=text_hash,
                engine=engine,
                text=text,
                file_path=file_path,
                file_name=f"{text_hash}.mp3",
                file_size_bytes=file_size_bytes,
            )
            self.db_session.add(tts_entry)

        await self.db_session.commit()
        await self.db_session.refresh(tts_entry)
