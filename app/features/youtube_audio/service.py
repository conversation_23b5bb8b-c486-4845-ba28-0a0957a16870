import re
import os
from typing import Optional, Dict, Any
from urllib.parse import urlparse, parse_qs
from sqlmodel.ext.asyncio.session import AsyncSession
from sqlalchemy.future import select
from loguru import logger

from app.features.youtube_audio.models import YouTubeAudio


def clean_youtube_url(url: str) -> str:
    logger.debug(f"Очистка URL: {url}")
    # Handle common short-form URLs (e.g., youtu.be)
    if "youtu.be" in url:
        match = re.search(r"youtu\.be/([a-zA-Z0-9_-]+)", url)
        if match:
            video_id = match.group(1)
            cleaned_url = f"https://www.youtube.com/watch?v={video_id}"
            logger.debug(f"Очищенный youtu.be URL до: {cleaned_url}")
            return cleaned_url

    # Extract video ID from full YouTube URL
    parsed_url = urlparse(url)
    if (
        parsed_url.hostname in ["www.youtube.com", "youtube.com"]
        and parsed_url.path == "/watch"
    ):
        query_params = parse_qs(parsed_url.query)
        if "v" in query_params:
            video_id = query_params["v"][0]
            cleaned_url = f"https://www.youtube.com/watch?v={video_id}"
            logger.debug(f"Очищенный полный YouTube URL до: {cleaned_url}")
            return cleaned_url

    logger.debug(f"Не удалось очистить или распознать формат YouTube URL: {url}")
    return url  # Return original if cannot clean


def extract_video_id(url: str) -> Optional[str]:
    # Try to extract from standard watch URL
    match = re.search(r"v=([a-zA-Z0-9_-]+)", url)
    if match:
        video_id = match.group(1)
        logger.debug(f"Извлечен video ID '{video_id}' из URL: {url}")
        return video_id

    # Try to extract from youtu.be short URL
    match = re.search(r"youtu\.be/([a-zA-Z0-9_-]+)", url)
    if match:
        video_id = match.group(1)
        logger.debug(f"Извлечен video ID '{video_id}' из youtu.be URL: {url}")
        return video_id

    logger.debug(f"Не удалось извлечь video ID из URL: {url}")
    return None


async def get_audio_from_db(
    db_session: AsyncSession, video_id: str
) -> Optional[YouTubeAudio]:
    logger.debug(f"Проверка кэша БД для YouTube аудио для video_id: {video_id}")
    result = await db_session.execute(
        select(YouTubeAudio).where(YouTubeAudio.video_id == video_id)
    )
    audio_entry = result.scalar_one_or_none()
    if audio_entry:
        logger.debug(f"Найден кэшированный аудиофайл для video_id: {video_id}")
    else:
        logger.debug(f"Кэшированный аудиофайл не найден для video_id: {video_id}")
    return audio_entry


async def save_audio_to_db(
    db_session: AsyncSession, audio_data: Dict[str, Any]
) -> YouTubeAudio:
    logger.debug(
        f"Сохранение аудиоданных в БД для video_id: {audio_data.get('video_id')}"
    )
    youtube_audio = YouTubeAudio(**audio_data)
    db_session.add(youtube_audio)
    await db_session.commit()
    await db_session.refresh(youtube_audio)
    logger.info(f"Аудиоданные сохранены в БД для video_id: {youtube_audio.video_id}")
    return youtube_audio


def delete_audio_file(file_path: str) -> bool:
    """Deletes the temporary audio file."""
    logger.debug(f"Попытка удаления временного аудиофайла: {file_path}")
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            logger.debug(f"Временный аудиофайл удален: {file_path}")
            return True
        else:
            logger.debug(f"Попытка удаления несуществующего файла: {file_path}")
            return False
    except OSError as e:
        logger.error(f"Ошибка при удалении временного файла {file_path}: {e}")
        return False
