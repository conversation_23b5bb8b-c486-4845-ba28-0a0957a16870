from pydantic import HttpUrl, Field
from sqlmodel import SQLModel
from datetime import datetime


class YouTubeAudioRequest(SQLModel):
    url: HttpUrl = Field(..., example="https://www.youtube.com/watch?v=dQw4w9WgXcQ")


class YouTubeAudioResult(SQLModel):
    video_id: str
    file_path: str
    file_name: str
    file_size_bytes: int
    created_at: datetime
    audio_stream_url: str  # New field for streaming
