[project]
name = "yt-subs-api18"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiohttp>=3.12.13",
    "aioredis>=2.0.1",
    "alembic>=1.16.2",
    "anyio[trio]>=4.9.0",
    "arq>=0.26.3",
    "asyncpg>=0.30.0",
    "edge-tts>=7.0.2",
    "fastapi[all]>=0.115.12",
    "ffmpeg-python>=0.2.0",
    "google-genai>=1.20.0",
    "google-generativeai>=0.8.5",
    "greenlet>=3.2.3",
    "httpx>=0.28.1",
    "loguru>=0.7.3",
    "markdown>=3.8.2",
    "mdformat>=0.7.22",
    "openai>=1.91.0",
    "psycopg2-binary>=2.9.10",
    "pytest>=8.4.0",
    "python-dotenv>=1.1.0",
    "questionary>=2.1.0",
    "redis>=5.3.0",
    "rich>=14.0.0",
    "send2trash>=1.8.3",
    "slowapi>=0.1.9",
    "sqlmodel>=0.0.24",
    "srt>=3.5.3",
    "sse-starlette>=2.3.6",
    "typer>=0.16.0",
    "xxhash>=3.5.0",
    "yt-dlp>=2025.6.9",
]

[project.scripts]
start_server = "runners.run_app_runner:run_server"
summaries = "client.run_summary:app"
summaries_alt = "client.run_summary_alt_ui:app"
prepare_files = "client.utils.prepare_files:main"

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]
where = [".", "runners"]
include = ["app*", "client*", "runners*"]
exclude = ["tmp*", "config*"]
