# Задача: За<PERSON><PERSON>на subprocess ffmpeg на Python SDK в функции _convert_wav_to_mp3

## Основная цель
Заменить вызов ffmpeg через subprocess в функции `_convert_wav_to_mp3` в файле `app/tasks/tts_utils/tts_client.py` на использование Python SDK для ffmpeg.

## Суть проекта
Проект представляет собой FastAPI приложение для работы с YouTube аудио, субтитрами, синтезом речи (TTS) и суммаризацией. Архитектура построена на асинхронных принципах с использованием FastAPI, ARQ (для очередей задач) и SQLModel (для работы с базой данных).

## Текущий статус
Завершена реализация новой версии функции `_convert_wav_to_mp3` с использованием библиотеки ffmpeg-python. Библиотека уже была добавлена в зависимости проекта в pyproject.toml.

## Результаты исследования
1. В проекте нет готовых Python SDK для работы с FFmpeg
2. В файле `youtube_audio_converter.py` также используется subprocess для вызова FFmpeg
3. Подходящие библиотеки для интеграции:
   - ffmpeg-python: обертка для FFmpeg с удобным API
   - pydub: высокоуровневая библиотека для работы с аудио, использующая FFmpeg под капотом

## План работы
- [x] Изучить текущую реализацию функции `_convert_wav_to_mp3`
- [x] Проверить зависимости проекта на наличие библиотек для работы с ffmpeg
- [x] Изучить другие части проекта, использующие ffmpeg
- [x] Выбрать подходящую библиотеку для работы с ffmpeg через Python
- [x] Установить и настроить выбранную библиотеку (ffmpeg-python уже была в зависимостях)
- [x] Реализовать новую версию функции `_convert_wav_to_mp3` с использованием выбранной библиотеки
- [x] Убедиться, что новая реализация корректно обрабатывает ошибки и логирует их
- [ ] Протестировать новую реализацию в контексте всего приложения
- [ ] Оценить производительность и надежность нового подхода
- [ ] Сделать выводы о целесообразности замены

## Текущий шаг
Тестирование новой реализации функции `_convert_wav_to_mp3` с использованием библиотеки ffmpeg-python в контексте всего приложения.